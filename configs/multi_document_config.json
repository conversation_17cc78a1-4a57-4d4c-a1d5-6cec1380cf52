{"model_name_or_path": "Qwen/Qwen3-0.6B", "dataset_path": "./documents", "output_dir": "./multi_doc_checkpoints", "learning_rate": 3e-05, "weight_decay": 0.01, "beta1": 0.9, "beta2": 0.95, "eps": 1e-08, "per_device_train_batch_size": 1, "per_device_eval_batch_size": 1, "gradient_accumulation_steps": 8, "max_seq_length": 2048, "num_train_epochs": 2, "max_train_steps": null, "warmup_steps": 200, "warmup_ratio": 0.0, "lr_scheduler_type": "cosine", "mixed_precision": "bf16", "gradient_clipping": 1.0, "gradient_checkpointing": true, "tensor_parallel_size": 1, "data_parallel_size": 1, "eval_steps": 100, "save_steps": 500, "logging_steps": 10, "eval_strategy": "steps", "save_strategy": "steps", "max_memory_per_gpu": null, "cpu_offload": false, "resume_from_checkpoint": null, "save_total_limit": 3, "preprocessing_num_workers": 4, "dataloader_num_workers": 0, "dataloader_pin_memory": true, "seed": 42, "ignore_data_skip": false, "remove_unused_columns": true, "_comment": "Multi-document training configuration", "_document_processing": {"concatenate_documents": false, "document_separator": "\n\n---\n\n", "chunk_overlap": 128, "min_chunk_size": 100, "supported_formats": [".txt", ".json", ".j<PERSON>l"], "recursive_search": true}}