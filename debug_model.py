#!/usr/bin/env python3
"""
Debug the model tensor shape issues.
"""

import torch
from nanovllm.training.from_scratch import get_model_config_for_size
from nanovllm.models.qwen3 import Qwen3ForCausalLM

def debug_model_shapes():
    """Debug model tensor shapes."""
    print("🔍 Debugging Model Tensor Shapes")
    print("=" * 50)
    
    # Create a tiny model config
    config = get_model_config_for_size("tiny", vocab_size=100)
    
    print("Model Configuration:")
    print(f"  vocab_size: {config.vocab_size}")
    print(f"  hidden_size: {config.hidden_size}")
    print(f"  num_attention_heads: {config.num_attention_heads}")
    print(f"  num_key_value_heads: {config.num_key_value_heads}")
    print(f"  head_dim: {config.hidden_size // config.num_attention_heads}")
    print(f"  config.head_dim: {getattr(config, 'head_dim', 'Not set')}")

    # Check all config attributes
    print(f"  All config attributes: {[attr for attr in dir(config) if not attr.startswith('_')]}")
    
    try:
        # Create model
        print("\n🏗️ Creating model...")
        model = Qwen3ForCausalLM(config)
        print("✅ Model created successfully")

        # Check actual model head_dim
        attn_layer = model.model.layers[0].self_attn
        print(f"Actual model head_dim: {attn_layer.head_dim}")
        print(f"Actual model total_num_heads: {attn_layer.total_num_heads}")
        print(f"Actual model num_heads: {attn_layer.num_heads}")
        
        # Test with simple input
        print("\n🧪 Testing forward pass...")
        batch_size = 1
        seq_len = 3
        
        # Create input
        input_ids = torch.randint(0, config.vocab_size, (batch_size, seq_len))
        positions = torch.arange(seq_len)
        
        print(f"Input shape: {input_ids.shape}")
        print(f"Positions shape: {positions.shape}")
        
        # Try forward pass
        with torch.no_grad():
            outputs = model(input_ids=input_ids, positions=positions)
            print(f"✅ Forward pass successful!")
            print(f"Output shape: {outputs.shape if hasattr(outputs, 'shape') else 'dict/other'}")
            
    except Exception as e:
        print(f"❌ Error: {e}")
        
        # Let's debug step by step
        print("\n🔧 Debugging step by step...")
        
        try:
            # Test just the embedding
            print("Testing embedding layer...")
            embed_layer = model.model.embed_tokens
            embedded = embed_layer(input_ids)
            print(f"Embedding output shape: {embedded.shape}")
            
            # Test first attention layer
            print("Testing first attention layer...")
            attn_layer = model.model.layers[0].self_attn
            
            # Check the QKV projection details
            print("Testing QKV projection...")
            qkv_proj = attn_layer.qkv_proj
            print(f"QKV proj num_heads: {qkv_proj.num_heads}")
            print(f"QKV proj num_kv_heads: {qkv_proj.num_kv_heads}")
            print(f"QKV proj head_size: {qkv_proj.head_size}")
            print(f"QKV proj total_num_heads: {qkv_proj.total_num_heads}")
            print(f"QKV proj total_num_kv_heads: {qkv_proj.total_num_kv_heads}")

            # Check the weight shape
            print(f"QKV weight shape: {qkv_proj.weight.shape}")

            qkv_out = qkv_proj(embedded.view(-1, config.hidden_size))
            print(f"QKV output shape: {qkv_out.shape}")

            # Check how it's split
            q_size = attn_layer.q_size
            kv_size = attn_layer.kv_size
            print(f"Expected q_size: {q_size}, kv_size: {kv_size}")
            print(f"Total expected: {q_size + 2 * kv_size}")

            q, k, v = qkv_out.split([q_size, kv_size, kv_size], dim=-1)
            print(f"Q shape: {q.shape}, K shape: {k.shape}, V shape: {v.shape}")

            # Check head reshaping
            num_heads = attn_layer.num_heads
            head_dim = attn_layer.head_dim
            print(f"num_heads: {num_heads}, head_dim: {head_dim}")

            # This is where the error likely occurs
            q_reshaped = q.view(-1, num_heads, head_dim)
            print(f"Q reshaped: {q_reshaped.shape}")
            
        except Exception as e2:
            print(f"❌ Step-by-step error: {e2}")
            import traceback
            traceback.print_exc()

if __name__ == "__main__":
    debug_model_shapes()
