import torch
from torch import nn
import triton
import triton.language as tl

# Optional flash attention import
try:
    from flash_attn import flash_attn_varlen_func, flash_attn_with_kvcache
    HAS_FLASH_ATTN = True
except ImportError:
    HAS_FLASH_ATTN = False
    flash_attn_varlen_func = None
    flash_attn_with_kvcache = None

from nanovllm.utils.context import get_context


@triton.jit
def store_kvcache_kernel(
    key_ptr,
    key_stride,
    value_ptr,
    value_stride,
    k_cache_ptr,
    v_cache_ptr,
    slot_mapping_ptr,
    D: tl.constexpr,
):
    idx = tl.program_id(0)
    key_offsets = idx * key_stride + tl.arange(0, D)
    value_offsets = idx * value_stride + tl.arange(0, D)
    key = tl.load(key_ptr + key_offsets)
    value = tl.load(value_ptr + value_offsets)
    slot = tl.load(slot_mapping_ptr + idx)
    cache_offsets = slot * D + tl.arange(0, D)
    tl.store(k_cache_ptr + cache_offsets, key)
    tl.store(v_cache_ptr + cache_offsets, value)


def store_kvcache(key: torch.Tensor, value: torch.Tensor, k_cache: torch.Tensor, v_cache: torch.Tensor, slot_mapping: torch.Tensor):
    N, num_heads, head_dim = key.shape
    D = num_heads * head_dim
    assert key.stride(-1) == 1 and value.stride(-1) == 1
    assert key.stride(1) == head_dim and value.stride(1) == head_dim
    assert k_cache.stride(1) == D and v_cache.stride(1) == D
    assert slot_mapping.numel() == N
    store_kvcache_kernel[(N,)](key, key.stride(0), value, value.stride(0), k_cache, v_cache, slot_mapping, D)


class Attention(nn.Module):

    def __init__(
        self,
        num_heads,
        head_dim,
        scale,
        num_kv_heads,
    ):
        super().__init__()
        self.num_heads = num_heads
        self.head_dim = head_dim
        self.scale = scale
        self.num_kv_heads = num_kv_heads
        self.k_cache = self.v_cache = torch.tensor([])

    def forward(self, q: torch.Tensor, k: torch.Tensor, v: torch.Tensor):
        o: torch.Tensor
        q = q.view(-1, self.num_heads, self.head_dim)
        k = k.view(-1, self.num_kv_heads, self.head_dim)
        v = v.view(-1, self.num_kv_heads, self.head_dim)

        # Check if we're in training mode (no proper context/cache available)
        context = get_context()
        k_cache = self.k_cache
        v_cache = self.v_cache

        # Check if we have proper inference setup
        has_proper_cache = (k_cache.numel() > 0 and v_cache.numel() > 0)
        has_slot_mapping = (context.slot_mapping is not None)

        if has_proper_cache and has_slot_mapping:
            # Inference mode with proper KV cache
            try:
                store_kvcache(k, v, k_cache, v_cache, context.slot_mapping)
                use_kv_cache = True
            except (RuntimeError, AttributeError, IndexError):
                # Fallback to training mode
                use_kv_cache = False
        else:
            # Training mode - no KV cache needed
            use_kv_cache = False

        if use_kv_cache and HAS_FLASH_ATTN:
            # Use flash attention if available (inference mode)
            if context.is_prefill:
                if context.block_tables is not None:    # prefix cache
                    k, v = k_cache, v_cache
                o = flash_attn_varlen_func(q, k, v,
                                           max_seqlen_q=context.max_seqlen_q, cu_seqlens_q=context.cu_seqlens_q,
                                           max_seqlen_k=context.max_seqlen_k, cu_seqlens_k=context.cu_seqlens_k,
                                           softmax_scale=self.scale, causal=True, block_table=context.block_tables)
            else:    # decode
                o = flash_attn_with_kvcache(q.unsqueeze(1), k_cache, v_cache,
                                            cache_seqlens=context.context_lens, block_table=context.block_tables,
                                            softmax_scale=self.scale, causal=True)
        elif use_kv_cache:
            # Fallback to standard attention (inference mode)
            if context.is_prefill:
                if context.block_tables is not None:    # prefix cache
                    k, v = k_cache, v_cache
                # Simple attention fallback for prefill
                q_reshaped = q.view(-1, self.num_heads, self.head_dim)
                k_reshaped = k.view(-1, self.num_heads, self.head_dim)
                v_reshaped = v.view(-1, self.num_heads, self.head_dim)

                # Basic scaled dot-product attention
                scores = torch.matmul(q_reshaped, k_reshaped.transpose(-2, -1)) * self.scale
                # Apply causal mask
                seq_len = q_reshaped.size(0)
                causal_mask = torch.triu(torch.ones(seq_len, seq_len, device=q.device), diagonal=1).bool()
                scores.masked_fill_(causal_mask, float('-inf'))
                attn_weights = torch.softmax(scores, dim=-1)
                o = torch.matmul(attn_weights, v_reshaped)
                o = o.view(-1, self.num_heads * self.head_dim)
            else:    # decode
                # Simple decode attention fallback
                q_reshaped = q.view(-1, self.num_heads, self.head_dim)
                # For decode, we need to attend to all cached keys/values
                # This is a simplified version - in practice you'd need proper cache handling
                k_reshaped = k_cache.view(-1, self.num_heads, self.head_dim)
                v_reshaped = v_cache.view(-1, self.num_heads, self.head_dim)

                scores = torch.matmul(q_reshaped, k_reshaped.transpose(-2, -1)) * self.scale
                attn_weights = torch.softmax(scores, dim=-1)
                o = torch.matmul(attn_weights, v_reshaped)
                o = o.view(-1, self.num_heads * self.head_dim)
        else:
            # Training mode - simple scaled dot-product attention
            # q, k, v are already reshaped to (-1, num_heads, head_dim)

            # Basic scaled dot-product attention
            scores = torch.matmul(q, k.transpose(-2, -1)) * self.scale

            # Apply causal mask for training
            seq_len = q.size(0)
            if seq_len > 1:  # Only apply mask if we have multiple tokens
                causal_mask = torch.triu(torch.ones(seq_len, seq_len, device=q.device), diagonal=1).bool()
                scores.masked_fill_(causal_mask, float('-inf'))

            attn_weights = torch.softmax(scores, dim=-1)
            o = torch.matmul(attn_weights, v)
            o = o.view(-1, self.num_heads * self.head_dim)

        return o
