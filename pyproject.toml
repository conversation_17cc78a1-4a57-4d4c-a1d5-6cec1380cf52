[build-system]
requires = ["setuptools>=61"]
build-backend = "setuptools.build_meta"

[project]
name = "nano-vllm"
version = "0.2.0"
authors = [{ name = "<PERSON>ng<PERSON>" }]
license = "MIT"
license-files = ["LICENSE"]
readme = "README.md"
description = "a lightweight vLLM implementation built from scratch"
requires-python = ">=3.10,<3.13"
dependencies = [
    "torch>=2.4.0",
    "triton>=3.0.0",
    "transformers>=4.51.0",
    "xxhash",
    "datasets>=2.0.0",
    "accelerate>=0.20.0",
]

[project.optional-dependencies]
training = [
    "datasets>=2.0.0",
    "accelerate>=0.20.0",
]
performance = [
    "flash-attn",
]
all = [
    "datasets>=2.0.0",
    "accelerate>=0.20.0",
    "flash-attn",
]

[project.urls]
Homepage="https://github.com/GeeeekExplorer/nano-vllm"

[tool.setuptools]
packages = ["nanovllm"]
